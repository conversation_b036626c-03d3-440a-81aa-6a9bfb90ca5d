#!/usr/bin/env python3
"""Create superuser script."""

import asyncio
import sys
from getpass import getpass

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import AsyncSessionLocal
from app.schemas.user import UserCreate
from app.services.user_service import UserService


async def create_superuser():
    """Create superuser interactively."""
    print("Creating superuser...")
    
    email = input("Email: ")
    username = input("Username: ")
    full_name = input("Full name (optional): ") or None
    password = getpass("Password: ")
    password_confirm = getpass("Confirm password: ")
    
    if password != password_confirm:
        print("Passwords don't match!")
        sys.exit(1)
    
    async with AsyncSessionLocal() as db:
        user_service = UserService(db)
        
        # Check if user already exists
        existing_user = await user_service.get_by_email(email)
        if existing_user:
            print(f"User with email {email} already exists!")
            sys.exit(1)
        
        existing_username = await user_service.get_by_username(username)
        if existing_username:
            print(f"User with username {username} already exists!")
            sys.exit(1)
        
        # Create user
        user_create = UserCreate(
            email=email,
            username=username,
            full_name=full_name,
            password=password,
        )
        
        user = await user_service.create(user_create)
        
        # Make user superuser
        user.is_superuser = True
        await db.commit()
        
        print(f"Superuser {user.email} created successfully!")


if __name__ == "__main__":
    asyncio.run(create_superuser())
