-- Initialize database for FastAPI GenAI Template

-- Create database if it doesn't exist
-- This is handled by Docker Compose environment variables

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create indexes for better performance (will be created by Alembic migrations)
-- These are just examples of what might be useful

-- Grant permissions
GRANT ALL PRIVILEGES ON DATABASE fastapi_genai TO postgres;
