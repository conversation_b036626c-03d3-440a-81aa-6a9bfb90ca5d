# Production-Ready FastAPI GenAI Template

A comprehensive, production-ready FastAPI template for GenAI applications with authentication, database integration, monitoring, and deployment features.

## Features

- 🚀 **FastAPI** with async/await support
- 🔐 **Authentication** (JWT, OAuth2, API Keys)
- 🗄️ **Database** integration with SQLAlchemy and Alembic
- 🤖 **GenAI Integration** (OpenAI, Hugging Face, Custom Models)
- 📊 **Monitoring** and logging
- 🐳 **Docker** containerization
- 🧪 **Testing** suite with pytest
- 📚 **API Documentation** with Swagger/OpenAPI
- 🔒 **Security** best practices
- ⚡ **Performance** optimizations

## Quick Start

1. Clone the repository
2. Copy `.env.example` to `.env` and configure
3. Install dependencies: `pip install -r requirements/dev.txt`
4. Run migrations: `alembic upgrade head`
5. Start the server: `uvicorn app.main:app --reload`

## API Endpoints

- **Health**: `GET /health`
- **Authentication**: `POST /api/v1/auth/login`
- **GenAI**: `POST /api/v1/genai/chat`
- **Users**: `GET /api/v1/users/me`

## Documentation

- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## Deployment

```bash
docker-compose up -d
```