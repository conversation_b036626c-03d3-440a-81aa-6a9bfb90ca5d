"""Helper utilities."""

import uuid
from datetime import datetime
from typing import Any, Dict, Optional

from fastapi import Request


def generate_request_id() -> str:
    """Generate unique request ID."""
    return str(uuid.uuid4())


def get_client_ip(request: Request) -> str:
    """Get client IP address."""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return forwarded.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    return request.client.host if request.client else "unknown"


def format_datetime(dt: Optional[datetime]) -> Optional[str]:
    """Format datetime to ISO string."""
    if dt is None:
        return None
    return dt.isoformat()


def calculate_cost(tokens: int, model: str) -> float:
    """Calculate cost based on tokens and model."""
    # Simplified cost calculation - adjust based on actual pricing
    cost_per_token = {
        "gpt-3.5-turbo": 0.000002,
        "gpt-4": 0.00003,
        "gpt-4-turbo": 0.00001,
    }
    
    return tokens * cost_per_token.get(model, 0.000002)


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe storage."""
    import re
    
    # Remove or replace unsafe characters
    filename = re.sub(r'[^\w\s-.]', '', filename)
    filename = re.sub(r'[-\s]+', '-', filename)
    return filename.strip('-.')


def validate_file_type(content_type: str, allowed_types: list) -> bool:
    """Validate file content type."""
    return content_type in allowed_types


def truncate_text(text: str, max_length: int = 100) -> str:
    """Truncate text to maximum length."""
    if len(text) <= max_length:
        return text
    return text[:max_length - 3] + "..."


def mask_sensitive_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """Mask sensitive data in dictionary."""
    sensitive_keys = {"password", "api_key", "secret", "token"}
    
    masked_data = {}
    for key, value in data.items():
        if key.lower() in sensitive_keys:
            masked_data[key] = "***"
        elif isinstance(value, dict):
            masked_data[key] = mask_sensitive_data(value)
        else:
            masked_data[key] = value
    
    return masked_data
