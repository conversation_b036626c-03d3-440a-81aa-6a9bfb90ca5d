"""GenAI schemas."""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field


class ChatMessageBase(BaseModel):
    """Base chat message schema."""
    role: str = Field(..., description="Message role: user, assistant, or system")
    content: str = Field(..., description="Message content")


class ChatMessageCreate(ChatMessageBase):
    """Chat message creation schema."""
    session_id: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class ChatMessage(ChatMessageBase):
    """Chat message schema."""
    id: int
    session_id: int
    tokens: Optional[int] = None
    cost: Optional[float] = None
    model_name: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime

    class Config:
        from_attributes = True


class ChatSessionBase(BaseModel):
    """Base chat session schema."""
    title: Optional[str] = None
    model_name: str = Field(..., description="AI model name")
    system_prompt: Optional[str] = None


class ChatSessionCreate(ChatSessionBase):
    """Chat session creation schema."""
    pass


class ChatSession(ChatSessionBase):
    """Chat session schema."""
    id: int
    user_id: int
    total_tokens: int = 0
    total_cost: float = 0.0
    created_at: datetime
    updated_at: datetime
    messages: List[ChatMessage] = []

    class Config:
        from_attributes = True


class ChatRequest(BaseModel):
    """Chat request schema."""
    message: str = Field(..., description="User message")
    session_id: Optional[int] = Field(None, description="Chat session ID")
    model: Optional[str] = Field(None, description="AI model to use")
    system_prompt: Optional[str] = Field(None, description="System prompt")
    max_tokens: Optional[int] = Field(None, description="Maximum tokens to generate")
    temperature: Optional[float] = Field(0.7, ge=0.0, le=2.0, description="Temperature for generation")
    stream: Optional[bool] = Field(False, description="Enable streaming response")


class ChatResponse(BaseModel):
    """Chat response schema."""
    message: str = Field(..., description="AI response")
    session_id: int = Field(..., description="Chat session ID")
    model: str = Field(..., description="AI model used")
    tokens: Optional[int] = Field(None, description="Tokens used")
    cost: Optional[float] = Field(None, description="Cost of the request")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")


class ModelInfo(BaseModel):
    """Model information schema."""
    name: str
    description: str
    provider: str
    max_tokens: int
    cost_per_token: Optional[float] = None


class HealthCheck(BaseModel):
    """Health check schema."""
    status: str
    timestamp: datetime
    version: str
    database: str
    redis: Optional[str] = None
