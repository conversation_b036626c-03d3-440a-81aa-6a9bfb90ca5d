"""Authentication endpoints."""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm

from app.core.auth import get_current_active_user
from app.dependencies import get_auth_service, get_user_service
from app.models.user import User
from app.schemas.user import <PERSON><PERSON><PERSON>, Token, User as UserSchema, UserCreate
from app.services.auth_service import AuthService
from app.services.user_service import UserService

router = APIRouter()


@router.post("/register", response_model=UserSchema)
async def register(
    user_create: UserCreate,
    auth_service: AuthService = Depends(get_auth_service),
):
    """Register new user."""
    return await auth_service.register_user(user_create)


@router.post("/login", response_model=Token)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    auth_service: AuthService = Depends(get_auth_service),
):
    """Login user and return tokens."""
    return await auth_service.login(form_data.username, form_data.password)


@router.post("/refresh", response_model=Token)
async def refresh_token(
    refresh_token: str,
    auth_service: AuthService = Depends(get_auth_service),
):
    """Refresh access token."""
    return await auth_service.refresh_token(refresh_token)


@router.post("/api-key", response_model=APIKey)
async def generate_api_key(
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service),
):
    """Generate API key for current user."""
    api_key = await user_service.generate_api_key(current_user.id)
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate API key"
        )
    
    return APIKey(api_key=api_key, created_at=current_user.updated_at)


@router.delete("/api-key")
async def revoke_api_key(
    current_user: User = Depends(get_current_active_user),
    user_service: UserService = Depends(get_user_service),
):
    """Revoke API key for current user."""
    success = await user_service.revoke_api_key(current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to revoke API key"
        )
    
    return {"message": "API key revoked successfully"}
