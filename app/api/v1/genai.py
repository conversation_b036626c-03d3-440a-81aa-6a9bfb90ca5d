"""GenAI endpoints."""

from typing import List

from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Request, status
from fastapi.responses import StreamingResponse

from app.core.auth import get_current_active_user
from app.dependencies import get_genai_service, limiter
from app.models.user import User
from app.schemas.genai import (
    ChatRequest,
    ChatResponse,
    ChatSession,
    ChatSessionCreate,
    ModelInfo,
)
from app.services.genai_service import GenAIService

router = APIRouter()


@router.post("/chat", response_model=ChatResponse)
@limiter.limit("10/minute")
async def chat(
    request: Request,
    chat_request: ChatRequest,
    current_user: User = Depends(get_current_active_user),
    genai_service: GenAIService = Depends(get_genai_service),
):
    """Chat with AI model."""
    try:
        if chat_request.stream:
            # Handle streaming response
            async def generate():
                session = None
                if chat_request.session_id:
                    session = await genai_service.get_session(
                        chat_request.session_id, current_user
                    )
                    if not session:
                        raise HTTPException(
                            status_code=status.HTTP_404_NOT_FOUND,
                            detail="Session not found"
                        )
                else:
                    session_create = ChatSessionCreate(
                        model_name=chat_request.model or "gpt-3.5-turbo",
                        system_prompt=chat_request.system_prompt,
                    )
                    session = await genai_service.create_session(current_user, session_create)
                
                # Add user message
                await genai_service.add_message(session, "user", chat_request.message)
                
                # Prepare messages for AI
                messages = []
                if session.system_prompt:
                    messages.append({"role": "system", "content": session.system_prompt})
                
                # Get recent messages
                from sqlalchemy import select
                from app.models.genai import ChatMessage
                
                result = await genai_service.db.execute(
                    select(ChatMessage)
                    .where(ChatMessage.session_id == session.id)
                    .order_by(ChatMessage.created_at)
                    .limit(20)
                )
                session_messages = result.scalars().all()
                
                for msg in session_messages:
                    messages.append({"role": msg.role, "content": msg.content})
                
                # Stream AI response
                full_response = ""
                async for chunk in genai_service.chat_stream_openai(
                    messages=messages,
                    model=chat_request.model,
                    max_tokens=chat_request.max_tokens,
                    temperature=chat_request.temperature,
                ):
                    full_response += chunk
                    yield f"data: {chunk}\n\n"
                
                # Add AI message after streaming
                await genai_service.add_message(
                    session, "assistant", full_response, model_name=chat_request.model
                )
                
                yield "data: [DONE]\n\n"
            
            return StreamingResponse(
                generate(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"},
            )
        
        else:
            # Handle regular response
            return await genai_service.process_chat_request(current_user, chat_request)
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat processing failed: {str(e)}"
        )


@router.post("/sessions", response_model=ChatSession)
async def create_session(
    session_create: ChatSessionCreate,
    current_user: User = Depends(get_current_active_user),
    genai_service: GenAIService = Depends(get_genai_service),
):
    """Create new chat session."""
    return await genai_service.create_session(current_user, session_create)


@router.get("/sessions", response_model=List[ChatSession])
async def get_sessions(
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_active_user),
    genai_service: GenAIService = Depends(get_genai_service),
):
    """Get user's chat sessions."""
    return await genai_service.get_user_sessions(current_user, skip=skip, limit=limit)


@router.get("/sessions/{session_id}", response_model=ChatSession)
async def get_session(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    genai_service: GenAIService = Depends(get_genai_service),
):
    """Get chat session by ID."""
    session = await genai_service.get_session(session_id, current_user)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Session not found"
        )
    return session


@router.get("/models", response_model=List[ModelInfo])
async def get_available_models():
    """Get available AI models."""
    return [
        ModelInfo(
            name="gpt-3.5-turbo",
            description="Fast and efficient model for most tasks",
            provider="OpenAI",
            max_tokens=4096,
            cost_per_token=0.000002,
        ),
        ModelInfo(
            name="gpt-4",
            description="Most capable model for complex tasks",
            provider="OpenAI",
            max_tokens=8192,
            cost_per_token=0.00003,
        ),
        ModelInfo(
            name="gpt-4-turbo",
            description="Latest GPT-4 model with improved performance",
            provider="OpenAI",
            max_tokens=128000,
            cost_per_token=0.00001,
        ),
    ]
