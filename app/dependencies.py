"""FastAPI dependencies."""

from typing import Generator

from fastapi import Depends, HTTPException, Request, status
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.database import get_async_session
from app.services.auth_service import AuthService
from app.services.genai_service import GenAIService
from app.services.user_service import UserService
from app.utils.helpers import generate_request_id

# Rate limiter
limiter = Limiter(key_func=get_remote_address)


def get_user_service(
    db: AsyncSession = Depends(get_async_session),
) -> UserService:
    """Get user service dependency."""
    return UserService(db)


def get_auth_service(
    db: AsyncSession = Depends(get_async_session),
) -> AuthService:
    """Get auth service dependency."""
    return AuthService(db)


def get_genai_service(
    db: AsyncSession = Depends(get_async_session),
) -> GenAIService:
    """Get GenAI service dependency."""
    return GenAIService(db)


async def add_request_id(request: Request) -> str:
    """Add request ID to request scope."""
    request_id = generate_request_id()
    request.scope["request_id"] = request_id
    return request_id


def validate_api_key(api_key: str) -> bool:
    """Validate API key format."""
    if not api_key:
        return False
    
    # Basic validation - adjust as needed
    return len(api_key) >= 32


def check_rate_limit(request: Request) -> None:
    """Check rate limit for request."""
    # This is handled by slowapi middleware
    pass
