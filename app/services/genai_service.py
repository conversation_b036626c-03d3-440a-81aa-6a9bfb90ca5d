"""GenAI service for AI model interactions."""

import asyncio
from typing import <PERSON><PERSON><PERSON><PERSON>ator, Dict, List, Optional

import openai
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from app.core.config import settings
from app.core.logging import get_logger
from app.models.genai import ChatMessage, ChatSession
from app.models.user import User
from app.schemas.genai import ChatRequest, ChatResponse, ChatSessionCreate

logger = get_logger(__name__)


class GenAIService:
    """GenAI service for AI model interactions."""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.openai_client = openai.AsyncOpenAI(api_key=settings.OPENAI_API_KEY)
    
    async def create_session(
        self, user: User, session_create: ChatSessionCreate
    ) -> ChatSession:
        """Create new chat session."""
        session = ChatSession(
            user_id=user.id,
            title=session_create.title,
            model_name=session_create.model_name,
            system_prompt=session_create.system_prompt,
        )
        
        self.db.add(session)
        await self.db.commit()
        await self.db.refresh(session)
        return session
    
    async def get_session(self, session_id: int, user: User) -> Optional[ChatSession]:
        """Get chat session by ID."""
        result = await self.db.execute(
            select(ChatSession)
            .options(selectinload(ChatSession.messages))
            .where(ChatSession.id == session_id, ChatSession.user_id == user.id)
        )
        return result.scalar_one_or_none()
    
    async def get_user_sessions(
        self, user: User, skip: int = 0, limit: int = 100
    ) -> List[ChatSession]:
        """Get user's chat sessions."""
        result = await self.db.execute(
            select(ChatSession)
            .where(ChatSession.user_id == user.id)
            .order_by(ChatSession.updated_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()
    
    async def add_message(
        self, session: ChatSession, role: str, content: str, **kwargs
    ) -> ChatMessage:
        """Add message to chat session."""
        message = ChatMessage(
            session_id=session.id,
            role=role,
            content=content,
            tokens=kwargs.get("tokens"),
            cost=kwargs.get("cost"),
            model_name=kwargs.get("model_name"),
            metadata=kwargs.get("metadata"),
        )
        
        self.db.add(message)
        await self.db.commit()
        await self.db.refresh(message)
        return message
    
    async def chat_with_openai(
        self, messages: List[Dict[str, str]], model: str = None, **kwargs
    ) -> Dict:
        """Chat with OpenAI API."""
        try:
            model = model or settings.OPENAI_MODEL
            
            response = await self.openai_client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=kwargs.get("max_tokens", settings.OPENAI_MAX_TOKENS),
                temperature=kwargs.get("temperature", 0.7),
                stream=kwargs.get("stream", False),
            )
            
            if kwargs.get("stream", False):
                return response
            
            return {
                "content": response.choices[0].message.content,
                "model": response.model,
                "tokens": response.usage.total_tokens,
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
            }
            
        except Exception as e:
            logger.error("OpenAI API error", error=str(e))
            raise
    
    async def chat_stream_openai(
        self, messages: List[Dict[str, str]], model: str = None, **kwargs
    ) -> AsyncGenerator[str, None]:
        """Stream chat with OpenAI API."""
        try:
            model = model or settings.OPENAI_MODEL
            
            stream = await self.openai_client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=kwargs.get("max_tokens", settings.OPENAI_MAX_TOKENS),
                temperature=kwargs.get("temperature", 0.7),
                stream=True,
            )
            
            async for chunk in stream:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            logger.error("OpenAI streaming error", error=str(e))
            raise
    
    async def process_chat_request(
        self, user: User, request: ChatRequest
    ) -> ChatResponse:
        """Process chat request."""
        # Get or create session
        if request.session_id:
            session = await self.get_session(request.session_id, user)
            if not session:
                raise ValueError("Session not found")
        else:
            session_create = ChatSessionCreate(
                model_name=request.model or settings.OPENAI_MODEL,
                system_prompt=request.system_prompt,
            )
            session = await self.create_session(user, session_create)
        
        # Add user message
        await self.add_message(session, "user", request.message)
        
        # Prepare messages for AI
        messages = []
        if session.system_prompt:
            messages.append({"role": "system", "content": session.system_prompt})
        
        # Get recent messages from session
        result = await self.db.execute(
            select(ChatMessage)
            .where(ChatMessage.session_id == session.id)
            .order_by(ChatMessage.created_at)
            .limit(20)  # Limit context window
        )
        session_messages = result.scalars().all()
        
        for msg in session_messages:
            messages.append({"role": msg.role, "content": msg.content})
        
        # Get AI response
        ai_response = await self.chat_with_openai(
            messages=messages,
            model=request.model,
            max_tokens=request.max_tokens,
            temperature=request.temperature,
        )
        
        # Add AI message
        await self.add_message(
            session,
            "assistant",
            ai_response["content"],
            tokens=ai_response.get("tokens"),
            model_name=ai_response.get("model"),
        )
        
        # Update session totals
        session.total_tokens += ai_response.get("tokens", 0)
        await self.db.commit()
        
        return ChatResponse(
            message=ai_response["content"],
            session_id=session.id,
            model=ai_response.get("model", request.model or settings.OPENAI_MODEL),
            tokens=ai_response.get("tokens"),
            cost=ai_response.get("cost"),
        )
