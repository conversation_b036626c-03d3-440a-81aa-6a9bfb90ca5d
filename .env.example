# Application
APP_NAME="FastAPI GenAI Template"
APP_VERSION="1.0.0"
DEBUG=true
ENVIRONMENT="development"
SECRET_KEY="your-super-secret-key-change-this-in-production"
API_V1_STR="/api/v1"

# Server
HOST="0.0.0.0"
PORT=8000
RELOAD=true

# Database
DATABASE_URL="postgresql://user:password@localhost:5432/fastapi_genai"
DATABASE_URL_SYNC="postgresql://user:password@localhost:5432/fastapi_genai"
# For SQLite (development)
# DATABASE_URL="sqlite:///./app.db"

# Redis (for caching and rate limiting)
REDIS_URL="redis://localhost:6379/0"

# Authentication
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=10080
ALGORITHM="HS256"

# OpenAI
OPENAI_API_KEY="your-openai-api-key"
OPENAI_MODEL="gpt-3.5-turbo"
OPENAI_MAX_TOKENS=1000

# Hugging Face
HUGGINGFACE_API_KEY="your-huggingface-api-key"
HUGGINGFACE_MODEL="microsoft/DialoGPT-medium"

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Logging
LOG_LEVEL="INFO"
LOG_FORMAT="json"

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60

# File Upload
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=["image/jpeg", "image/png", "text/plain", "application/pdf"]

# Monitoring
SENTRY_DSN=""
ENABLE_METRICS=true

# Email (optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=""
SMTP_USER=""
SMTP_PASSWORD=""
EMAILS_FROM_EMAIL=""
EMAILS_FROM_NAME=""

# Security
BCRYPT_ROUNDS=12
API_KEY_HEADER="X-API-Key"
